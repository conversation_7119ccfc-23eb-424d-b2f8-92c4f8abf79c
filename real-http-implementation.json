{"name": "Real HTTP Implementation with Retry Logic", "nodes": [{"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 5}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-480, -180], "id": "schedule-trigger", "name": "Schedule Trigger"}, {"parameters": {"jsCode": "// Configuration and Circuit Breaker Check\nconst config = {\n  maxRetries: 3,\n  baseDelay: 1000,\n  maxDelay: 30000,\n  backoffMultiplier: 2,\n  timeout: 30000\n};\n\nreturn [{ json: { config, attempt: 1, shouldRetry: true } }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-280, -180], "id": "init-config", "name": "Initialize Config"}, {"parameters": {"url": "https://nodejs-serverless-function-express-tau-livid.vercel.app/api/health", "options": {"timeout": "={{ $json.config.timeout }}", "retry": {"enabled": false}, "response": {"response": {"neverError": true, "responseFormat": "json"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-80, -180], "id": "http-request-attempt", "name": "HTTP Request Attempt"}, {"parameters": {"jsCode": "// Process HTTP Response and Determine Next Action\nconst item = $input.item.json;\nconst startTime = Date.now();\n\n// Check if request was successful\nconst isSuccess = item.statusCode >= 200 && item.statusCode < 300;\nconst responseTime = Date.now() - startTime;\n\nif (isSuccess) {\n  // Success - reset retry logic\n  return [{\n    json: {\n      success: true,\n      data: item.body || item,\n      responseTime: responseTime,\n      attempt: $node[\"Initialize Config\"].json.attempt,\n      statusCode: item.statusCode,\n      endpoint: 'Main API',\n      timestamp: new Date().toISOString()\n    }\n  }];\n} else {\n  // Failure - check if we should retry\n  const config = $node[\"Initialize Config\"].json.config;\n  const currentAttempt = $node[\"Initialize Config\"].json.attempt;\n  \n  const shouldRetry = currentAttempt < config.maxRetries && \n                     ![401, 403, 404].includes(item.statusCode); // Don't retry on auth/not found errors\n  \n  if (shouldRetry) {\n    // Calculate exponential backoff delay\n    const delay = Math.min(\n      config.baseDelay * Math.pow(config.backoffMultiplier, currentAttempt - 1),\n      config.maxDelay\n    );\n    \n    return [{\n      json: {\n        success: false,\n        shouldRetry: true,\n        attempt: currentAttempt + 1,\n        delay: delay,\n        error: `HTTP ${item.statusCode}: ${item.statusMessage || 'Request failed'}`,\n        statusCode: item.statusCode,\n        config: config\n      }\n    }];\n  } else {\n    // Max retries reached or non-retryable error\n    return [{\n      json: {\n        success: false,\n        shouldRetry: false,\n        attempt: currentAttempt,\n        error: `HTTP ${item.statusCode}: ${item.statusMessage || 'Request failed'}`,\n        statusCode: item.statusCode,\n        endpoint: 'Main API',\n        timestamp: new Date().toISOString()\n      }\n    }];\n  }\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [120, -180], "id": "process-response", "name": "Process Response"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "should-retry", "leftValue": "={{ $json.shouldRetry }}", "rightValue": "true", "operator": {"type": "boolean", "operation": "equals"}}]}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [320, -180], "id": "check-retry", "name": "Should <PERSON><PERSON>?"}, {"parameters": {"jsCode": "// Exponential Backoff Delay\nconst delay = $json.delay || 1000;\n\n// Wait for the calculated delay\nreturn new Promise(resolve => {\n  setTimeout(() => {\n    resolve([{\n      json: {\n        config: $json.config,\n        attempt: $json.attempt,\n        shouldRetry: true,\n        delayCompleted: true,\n        timestamp: new Date().toISOString()\n      }\n    }]);\n  }, delay);\n});"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [520, -80], "id": "exponential-backoff", "name": "Exponential Backoff Delay"}, {"parameters": {"url": "https://nodejs-serverless-function-express-tau-livid.vercel.app/api/health", "options": {"timeout": "={{ $json.config.timeout }}", "retry": {"enabled": false}, "response": {"response": {"neverError": true, "responseFormat": "json"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [720, -80], "id": "http-retry-request", "name": "HTTP Retry Request"}, {"parameters": {"jsCode": "// Final Response Processing (same logic as initial processing)\nconst item = $input.item.json;\nconst startTime = Date.now();\n\n// Check if request was successful\nconst isSuccess = item.statusCode >= 200 && item.statusCode < 300;\nconst responseTime = Date.now() - startTime;\nconst currentAttempt = $node[\"Exponential Backoff Delay\"].json.attempt;\n\nif (isSuccess) {\n  // Success after retry\n  return [{\n    json: {\n      success: true,\n      data: item.body || item,\n      responseTime: responseTime,\n      attempt: currentAttempt,\n      statusCode: item.statusCode,\n      endpoint: 'Main API',\n      timestamp: new Date().toISOString(),\n      recoveredAfterRetry: true\n    }\n  }];\n} else {\n  // Still failing - check if we should retry again\n  const config = $node[\"Exponential Backoff Delay\"].json.config;\n  \n  const shouldRetry = currentAttempt < config.maxRetries && \n                     ![401, 403, 404].includes(item.statusCode);\n  \n  if (shouldRetry) {\n    // Continue retry loop\n    const delay = Math.min(\n      config.baseDelay * Math.pow(config.backoffMultiplier, currentAttempt - 1),\n      config.maxDelay\n    );\n    \n    return [{\n      json: {\n        success: false,\n        shouldRetry: true,\n        attempt: currentAttempt + 1,\n        delay: delay,\n        error: `HTTP ${item.statusCode}: ${item.statusMessage || 'Request failed'}`,\n        statusCode: item.statusCode,\n        config: config\n      }\n    }];\n  } else {\n    // Final failure\n    return [{\n      json: {\n        success: false,\n        shouldRetry: false,\n        attempt: currentAttempt,\n        error: `HTTP ${item.statusCode}: ${item.statusMessage || 'Request failed'}`,\n        statusCode: item.statusCode,\n        endpoint: 'Main API',\n        timestamp: new Date().toISOString(),\n        finalFailure: true\n      }\n    }];\n  }\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [920, -80], "id": "process-retry-response", "name": "Process Retry Response"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "continue-retry", "leftValue": "={{ $json.shouldRetry }}", "rightValue": "true", "operator": {"type": "boolean", "operation": "equals"}}]}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1120, -80], "id": "check-continue-retry", "name": "Continue Retry?"}, {"parameters": {"jsCode": "// Merge successful and failed responses for final processing\nconst item = $input.item.json;\n\n// This node receives data from both success and failure paths\n// Format the final result consistently\nreturn [{\n  json: {\n    ...item,\n    finalResult: true,\n    processedAt: new Date().toISOString()\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [320, -380], "id": "merge-results", "name": "Merge Results"}], "connections": {"Schedule Trigger": {"main": [[{"node": "Initialize Config", "type": "main", "index": 0}]]}, "Initialize Config": {"main": [[{"node": "HTTP Request Attempt", "type": "main", "index": 0}]]}, "HTTP Request Attempt": {"main": [[{"node": "Process Response", "type": "main", "index": 0}]]}, "Process Response": {"main": [[{"node": "Should <PERSON><PERSON>?", "type": "main", "index": 0}]]}, "Should Retry?": {"main": [[{"node": "Exponential Backoff Delay", "type": "main", "index": 0}], [{"node": "Merge Results", "type": "main", "index": 0}]]}, "Exponential Backoff Delay": {"main": [[{"node": "HTTP Retry Request", "type": "main", "index": 0}]]}, "HTTP Retry Request": {"main": [[{"node": "Process Retry Response", "type": "main", "index": 0}]]}, "Process Retry Response": {"main": [[{"node": "Continue Retry?", "type": "main", "index": 0}]]}, "Continue Retry?": {"main": [[{"node": "Exponential Backoff Delay", "type": "main", "index": 0}], [{"node": "Merge Results", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}}