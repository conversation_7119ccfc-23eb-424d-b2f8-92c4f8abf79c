const item = $input.item;
const output = {
  status: "unknown",
  summary: "No summary available",
  issues: [],
  services: {},
  timestamp: new Date().toISOString(),
};

try {
  if (item.json.status) {
    return [{ json: item.json }];
  }

  const errorMsg = item.json?.error?.message;
  if (typeof errorMsg === "string" && errorMsg.includes("{")) {
    // Extract JSON from messages like: '500 - "{JSON_CONTENT}"'
    const match = errorMsg.match(/"({[\s\S]*})"/); 
    if (match && match[1]) {
      // Unescape the JSON string
      const jsonString = match[1].replace(/\\"/g, '"');
      const parsed = JSON.parse(jsonString);
      output.status = parsed.status || "unhealthy";
      output.summary = parsed.summary || "System issue detected";
      output.issues = parsed.issues || [];
      output.services = parsed.services || {};
      output.timestamp = parsed.timestamp || output.timestamp;
      return [{ json: output }];
    }
    
    // Fallback: try direct JSON match
    const directMatch = errorMsg.match(/{[\s\S]*}/);
    if (directMatch && directMatch[0]) {
      const parsed = JSON.parse(directMatch[0]);
      output.status = parsed.status || "unhealthy";
      output.summary = parsed.summary || "System issue detected";
      output.issues = parsed.issues || [];
      output.services = parsed.services || {};
      output.timestamp = parsed.timestamp || output.timestamp;
      return [{ json: output }];
    }
  }

  output.status = "unhealthy";
  output.summary = errorMsg?.slice(0, 300) || "Unrecognized server error.";
} catch (err) {
  output.status = "unhealthy";
  output.summary = `Parse error: ${err.message}`;
}

return [{ json: output }];