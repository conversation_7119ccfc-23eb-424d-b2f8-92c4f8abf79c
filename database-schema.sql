-- Enhanced Database Schema for N8N Health Monitoring System
-- This includes circuit breaker state management and improved logging

-- Circuit Breaker State Table
CREATE TABLE CircuitBreakerState (
    id INT IDENTITY(1,1) PRIMARY KEY,
    endpoint_name NVARCHAR(255) NOT NULL UNIQUE,
    state NVARCHAR(20) NOT NULL DEFAULT 'CLOSED', -- <PERSON>LOSED, OPEN, HALF_OPEN
    failure_count INT NOT NULL DEFAULT 0,
    last_failure_time DATETIME2 NULL,
    next_attempt_time DATETIME2 NULL,
    created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    updated_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    
    CONSTRAINT CK_CircuitBreakerState_State 
        CHECK (state IN ('CLOSED', 'OPEN', 'HALF_OPEN')),
    CONSTRAINT CK_CircuitBreakerState_FailureCount 
        CHECK (failure_count >= 0)
);

-- Enhanced System Status History Table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'SystemStatusHistory')
BEGIN
    CREATE TABLE SystemStatusHistory (
        id INT IDENTITY(1,1) PRIMARY KEY,
        endpoint_name NVARCHAR(255) NOT NULL,
        status NVARCHAR(50) NOT NULL,
        response_time_ms INT NULL,
        attempt_count INT NOT NULL DEFAULT 1,
        error_message NVARCHAR(MAX) NULL,
        issues NVARCHAR(MAX) NULL,
        services NVARCHAR(MAX) NULL,
        summary NVARCHAR(MAX) NULL,
        severity_level NVARCHAR(20) NOT NULL DEFAULT 'INFO',
        circuit_breaker_state NVARCHAR(20) NULL,
        timestamp DATETIME2 NOT NULL DEFAULT GETDATE(),
        
        CONSTRAINT CK_SystemStatusHistory_Severity 
            CHECK (severity_level IN ('CRITICAL', 'WARNING', 'INFO')),
        CONSTRAINT CK_SystemStatusHistory_ResponseTime 
            CHECK (response_time_ms >= 0 OR response_time_ms IS NULL),
        CONSTRAINT CK_SystemStatusHistory_AttemptCount 
            CHECK (attempt_count > 0)
    );
END

-- Current System Status Table (for quick lookups)
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'SystemStatus')
BEGIN
    CREATE TABLE SystemStatus (
        id INT IDENTITY(1,1) PRIMARY KEY,
        endpoint_name NVARCHAR(255) NOT NULL UNIQUE,
        current_status NVARCHAR(50) NOT NULL,
        last_check_time DATETIME2 NOT NULL DEFAULT GETDATE(),
        last_success_time DATETIME2 NULL,
        consecutive_failures INT NOT NULL DEFAULT 0,
        average_response_time_ms FLOAT NULL,
        uptime_percentage DECIMAL(5,2) NULL,
        created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
        updated_at DATETIME2 NOT NULL DEFAULT GETDATE()
    );
END

-- Alert History Table (to prevent spam and track alert frequency)
CREATE TABLE AlertHistory (
    id INT IDENTITY(1,1) PRIMARY KEY,
    endpoint_name NVARCHAR(255) NOT NULL,
    alert_type NVARCHAR(50) NOT NULL, -- EMAIL, TELEGRAM, SLACK, etc.
    severity_level NVARCHAR(20) NOT NULL,
    message_content NVARCHAR(MAX) NULL,
    sent_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    recipient NVARCHAR(255) NULL,
    
    CONSTRAINT CK_AlertHistory_Severity 
        CHECK (severity_level IN ('CRITICAL', 'WARNING', 'INFO')),
    CONSTRAINT CK_AlertHistory_AlertType 
        CHECK (alert_type IN ('EMAIL', 'TELEGRAM', 'SLACK', 'WEBHOOK'))
);

-- Performance Metrics Table (for trend analysis)
CREATE TABLE PerformanceMetrics (
    id INT IDENTITY(1,1) PRIMARY KEY,
    endpoint_name NVARCHAR(255) NOT NULL,
    metric_name NVARCHAR(100) NOT NULL,
    metric_value FLOAT NOT NULL,
    metric_unit NVARCHAR(20) NULL,
    recorded_at DATETIME2 NOT NULL DEFAULT GETDATE(),
    
    INDEX IX_PerformanceMetrics_Endpoint_Time (endpoint_name, recorded_at),
    INDEX IX_PerformanceMetrics_Metric_Time (metric_name, recorded_at)
);

-- Indexes for better performance
CREATE INDEX IX_CircuitBreakerState_Endpoint ON CircuitBreakerState(endpoint_name);
CREATE INDEX IX_SystemStatusHistory_Timestamp ON SystemStatusHistory(timestamp DESC);
CREATE INDEX IX_SystemStatusHistory_Endpoint_Time ON SystemStatusHistory(endpoint_name, timestamp DESC);
CREATE INDEX IX_SystemStatusHistory_Severity ON SystemStatusHistory(severity_level);
CREATE INDEX IX_AlertHistory_Endpoint_Time ON AlertHistory(endpoint_name, sent_at DESC);
CREATE INDEX IX_AlertHistory_Severity_Time ON AlertHistory(severity_level, sent_at DESC);

-- Insert initial circuit breaker state
INSERT INTO CircuitBreakerState (endpoint_name, state, failure_count)
VALUES ('Main API', 'CLOSED', 0);

-- Insert initial system status
INSERT INTO SystemStatus (endpoint_name, current_status, consecutive_failures)
VALUES ('Main API', 'unknown', 0);

-- Stored Procedures for common operations

-- Procedure to check if we should send an alert (prevent spam)
CREATE OR ALTER PROCEDURE CheckAlertCooldown
    @EndpointName NVARCHAR(255),
    @SeverityLevel NVARCHAR(20),
    @CooldownMinutes INT = 15
AS
BEGIN
    DECLARE @LastAlertTime DATETIME2;
    DECLARE @CanSendAlert BIT = 1;
    
    SELECT TOP 1 @LastAlertTime = sent_at
    FROM AlertHistory
    WHERE endpoint_name = @EndpointName 
      AND severity_level = @SeverityLevel
    ORDER BY sent_at DESC;
    
    IF @LastAlertTime IS NOT NULL 
       AND DATEDIFF(MINUTE, @LastAlertTime, GETDATE()) < @CooldownMinutes
    BEGIN
        SET @CanSendAlert = 0;
    END
    
    SELECT @CanSendAlert as CanSendAlert, 
           @LastAlertTime as LastAlertTime,
           DATEDIFF(MINUTE, @LastAlertTime, GETDATE()) as MinutesSinceLastAlert;
END

-- Procedure to calculate uptime percentage
CREATE OR ALTER PROCEDURE CalculateUptimePercentage
    @EndpointName NVARCHAR(255),
    @HoursBack INT = 24
AS
BEGIN
    DECLARE @TotalChecks INT;
    DECLARE @SuccessfulChecks INT;
    DECLARE @UptimePercentage DECIMAL(5,2);
    
    SELECT @TotalChecks = COUNT(*),
           @SuccessfulChecks = SUM(CASE WHEN status = 'healthy' THEN 1 ELSE 0 END)
    FROM SystemStatusHistory
    WHERE endpoint_name = @EndpointName
      AND timestamp >= DATEADD(HOUR, -@HoursBack, GETDATE());
    
    IF @TotalChecks > 0
        SET @UptimePercentage = (@SuccessfulChecks * 100.0) / @TotalChecks
    ELSE
        SET @UptimePercentage = 0;
    
    SELECT @UptimePercentage as UptimePercentage,
           @TotalChecks as TotalChecks,
           @SuccessfulChecks as SuccessfulChecks;
END

-- Data retention procedure (run weekly)
CREATE OR ALTER PROCEDURE CleanupOldData
    @RetentionDays INT = 90
AS
BEGIN
    DECLARE @CutoffDate DATETIME2 = DATEADD(DAY, -@RetentionDays, GETDATE());
    
    -- Clean up old system status history
    DELETE FROM SystemStatusHistory 
    WHERE timestamp < @CutoffDate;
    
    -- Clean up old alert history
    DELETE FROM AlertHistory 
    WHERE sent_at < @CutoffDate;
    
    -- Clean up old performance metrics
    DELETE FROM PerformanceMetrics 
    WHERE recorded_at < @CutoffDate;
    
    -- Return cleanup stats
    SELECT 
        @@ROWCOUNT as TotalRowsDeleted,
        @CutoffDate as CutoffDate;
END

-- View for current system health dashboard
CREATE OR ALTER VIEW SystemHealthDashboard AS
SELECT 
    ss.endpoint_name,
    ss.current_status,
    ss.last_check_time,
    ss.last_success_time,
    ss.consecutive_failures,
    ss.average_response_time_ms,
    ss.uptime_percentage,
    cb.state as circuit_breaker_state,
    cb.failure_count as circuit_breaker_failures,
    cb.next_attempt_time as circuit_breaker_next_attempt,
    CASE 
        WHEN ss.consecutive_failures >= 5 THEN 'CRITICAL'
        WHEN ss.consecutive_failures >= 2 THEN 'WARNING'
        ELSE 'INFO'
    END as current_severity
FROM SystemStatus ss
LEFT JOIN CircuitBreakerState cb ON ss.endpoint_name = cb.endpoint_name;