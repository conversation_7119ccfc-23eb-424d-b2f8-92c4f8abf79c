{"name": "Configuration Management for Health Monitor", "description": "Centralized configuration management for the enhanced health monitoring system", "config": {"version": "2.0.0", "lastUpdated": "2025-06-27T16:45:00Z", "monitoring": {"scheduleIntervalMinutes": 5, "healthCheckTimeout": 30000, "maxConcurrentChecks": 3, "enableDetailedLogging": true}, "httpRequests": {"timeout": 30000, "maxRetries": 3, "baseDelay": 1000, "maxDelay": 30000, "backoffMultiplier": 2, "retryableStatusCodes": [500, 502, 503, 504, 408, 429], "nonRetryableStatusCodes": [400, 401, 403, 404, 422], "userAgent": "N8N-Health-Monitor/2.0"}, "circuitBreaker": {"failureThreshold": 5, "recoveryTimeoutMs": 300000, "halfOpenMaxCalls": 3, "halfOpenSuccessThreshold": 2, "enableCircuitBreaker": true, "monitoringWindowMs": 60000}, "endpoints": [{"id": "main-api", "name": "Main API", "url": "https://nodejs-serverless-function-express-tau-livid.vercel.app/api/health", "method": "GET", "critical": true, "expectedStatusCode": 200, "expectedResponseTimeMs": 2000, "customHeaders": {"Accept": "application/json", "User-Agent": "N8N-Health-Monitor/2.0"}, "healthCheckPath": "/health", "tags": ["api", "critical", "production"]}, {"id": "database-health", "name": "Database Health", "url": "https://your-api.com/health/database", "method": "GET", "critical": true, "expectedStatusCode": 200, "expectedResponseTimeMs": 1000, "tags": ["database", "critical", "infrastructure"]}, {"id": "cache-health", "name": "Cache Health", "url": "https://your-api.com/health/cache", "method": "GET", "critical": false, "expectedStatusCode": 200, "expectedResponseTimeMs": 500, "tags": ["cache", "performance", "infrastructure"]}], "alerting": {"enabled": true, "cooldownMinutes": 15, "escalationEnabled": true, "escalationDelayMinutes": 30, "maxAlertsPerHour": 10, "quietHours": {"enabled": false, "startTime": "22:00", "endTime": "08:00", "timezone": "UTC"}, "severityLevels": {"CRITICAL": {"emoji": "🔴", "color": "#FF0000", "priority": "HIGH", "channels": ["email", "telegram", "slack"], "escalate": true, "cooldownMinutes": 5}, "WARNING": {"emoji": "🟡", "color": "#FFA500", "priority": "MEDIUM", "channels": ["email", "telegram"], "escalate": false, "cooldownMinutes": 15}, "INFO": {"emoji": "🟢", "color": "#00FF00", "priority": "LOW", "channels": ["telegram"], "escalate": false, "cooldownMinutes": 60}}, "channels": {"email": {"enabled": true, "recipients": ["<EMAIL>"], "subjectPrefix": "[HEALTH MONITOR]", "includeDetails": true, "htmlFormat": false}, "telegram": {"enabled": true, "chatId": "642629143", "parseMode": "<PERSON><PERSON>", "disableWebPagePreview": true, "includeButtons": false}, "slack": {"enabled": false, "webhook": "", "channel": "#alerts", "username": "Health Monitor", "iconEmoji": ":warning:"}, "webhook": {"enabled": false, "url": "", "method": "POST", "headers": {"Content-Type": "application/json"}}}}, "database": {"connectionString": "Server=your-server;Database=HealthMonitor;Integrated Security=true;", "commandTimeout": 30, "retentionDays": 90, "enablePerformanceMetrics": true, "batchSize": 100, "tables": {"circuitBreakerState": "CircuitBreakerState", "systemStatusHistory": "SystemStatusHistory", "alertHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "performanceMetrics": "PerformanceMetrics", "systemStatus": "SystemStatus"}}, "performance": {"responseTimeThresholds": {"excellent": 500, "good": 1000, "acceptable": 2000, "poor": 5000, "critical": 10000}, "uptimeThresholds": {"excellent": 99.9, "good": 99.5, "acceptable": 99.0, "poor": 95.0}, "enableTrendAnalysis": true, "trendAnalysisWindowHours": 24}, "security": {"enableApiKeyAuth": false, "apiKey": "", "enableBasicAuth": false, "username": "", "password": "", "enableTLS": true, "validateCertificates": true, "allowSelfSignedCerts": false}, "logging": {"level": "INFO", "enableConsoleLogging": true, "enableFileLogging": false, "logFilePath": "/var/log/n8n-health-monitor.log", "maxLogFileSize": "10MB", "maxLogFiles": 5, "logFormat": "json", "includeStackTrace": true}, "features": {"enableCircuitBreaker": true, "enableRetryLogic": true, "enableExponentialBackoff": true, "enableAlertCooldown": true, "enablePerformanceTracking": true, "enableTrendAnalysis": true, "enableMaintenanceMode": false, "enableHealthCheckValidation": true}, "maintenanceMode": {"enabled": false, "startTime": null, "endTime": null, "reason": "", "disableAlerts": true, "disableHealthChecks": false, "notifyBeforeStart": true, "notifyAfterEnd": true}, "validation": {"enableResponseValidation": true, "expectedFields": ["status", "timestamp"], "requiredFields": ["status"], "validateJsonStructure": true, "customValidationRules": []}}, "environments": {"development": {"monitoring": {"scheduleIntervalMinutes": 10}, "alerting": {"enabled": false}, "logging": {"level": "DEBUG"}}, "staging": {"monitoring": {"scheduleIntervalMinutes": 5}, "alerting": {"cooldownMinutes": 30, "channels": {"email": {"enabled": false}}}}, "production": {"monitoring": {"scheduleIntervalMinutes": 3}, "alerting": {"cooldownMinutes": 10}, "logging": {"level": "WARN"}}}, "metadata": {"configVersion": "2.0.0", "compatibleN8NVersion": ">=1.0.0", "author": "Enhanced Health Monitor", "description": "Comprehensive configuration for N8N health monitoring system with retry logic, circuit breaker, and enhanced alerting", "tags": ["health-check", "monitoring", "alerting", "circuit-breaker", "retry-logic"], "lastModified": "2025-06-27T16:45:00Z", "checksum": "sha256:abc123..."}}