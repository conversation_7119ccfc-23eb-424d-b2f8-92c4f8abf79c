{
  "name": "Enhanced Health Check with Retry Logic",
  "nodes": [
    {
      "parameters": {
        "rule": {
          "interval": [
            {
              "field": "minutes",
              "minutesInterval": 5
            }
          ]
        }
      },
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.2,
      "position": [-480, -180],
      "id": "43777f46-fa19-46c8-89a4-5aa77445db2e",
      "name": "Schedule Trigger"
    },
    {
      "parameters": {
        "jsCode": "// Configuration for monitoring system\nconst config = {\n  http: {\n    timeout: 30000, // 30 seconds\n    maxRetries: 3,\n    baseDelay: 1000, // 1 second\n    maxDelay: 30000, // 30 seconds\n    backoffMultiplier: 2\n  },\n  circuitBreaker: {\n    failureThreshold: 5,\n    recoveryTimeout: 300000, // 5 minutes\n    halfOpenMaxCalls: 3\n  },\n  endpoints: [\n    {\n      url: \"https://nodejs-serverless-function-express-tau-livid.vercel.app/api/health\",\n      name: \"Main API\",\n      critical: true\n    }\n  ],\n  alerting: {\n    cooldownMinutes: 15,\n    severityLevels: {\n      CRITICAL: { emoji: \"🔴\", color: \"#FF0000\" },\n      WARNING: { emoji: \"🟡\", color: \"#FFA500\" },\n      INFO: { emoji: \"🟢\", color: \"#00FF00\" }\n    }\n  }\n};\n\nreturn [{ json: { config } }];"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [-280, -180],
      "id": "config-node",
      "name": "Configuration"
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "-- Check circuit breaker state\nSELECT \n  endpoint_name,\n  state,\n  failure_count,\n  last_failure_time,\n  next_attempt_time\nFROM CircuitBreakerState \nWHERE endpoint_name = 'Main API';"
      },
      "type": "n8n-nodes-base.microsoftSql",
      "typeVersion": 1.1,
      "position": [-80, -180],
      "id": "check-circuit-breaker",
      "name": "Check Circuit Breaker"
    },
    {
      "parameters": {
        "jsCode": "// Enhanced HTTP Request with Retry Logic and Circuit Breaker\nconst items = $input.all();\nconst config = items[0].json.config;\nconst circuitState = items[1].json || [];\n\nclass CircuitBreaker {\n  constructor(endpoint, state = null) {\n    this.endpoint = endpoint;\n    this.state = state ? state.state : 'CLOSED';\n    this.failureCount = state ? state.failure_count : 0;\n    this.lastFailureTime = state ? new Date(state.last_failure_time) : null;\n    this.nextAttemptTime = state ? new Date(state.next_attempt_time) : null;\n  }\n\n  canExecute() {\n    const now = new Date();\n    \n    switch (this.state) {\n      case 'CLOSED':\n        return true;\n      case 'OPEN':\n        return now >= this.nextAttemptTime;\n      case 'HALF_OPEN':\n        return true;\n      default:\n        return true;\n    }\n  }\n\n  onSuccess() {\n    this.state = 'CLOSED';\n    this.failureCount = 0;\n    this.lastFailureTime = null;\n    this.nextAttemptTime = null;\n  }\n\n  onFailure() {\n    this.failureCount++;\n    this.lastFailureTime = new Date();\n    \n    if (this.failureCount >= config.circuitBreaker.failureThreshold) {\n      this.state = 'OPEN';\n      this.nextAttemptTime = new Date(Date.now() + config.circuitBreaker.recoveryTimeout);\n    }\n  }\n\n  toHalfOpen() {\n    this.state = 'HALF_OPEN';\n  }\n}\n\nasync function makeHttpRequestWithRetry(url, maxRetries = 3) {\n  let lastError = null;\n  \n  for (let attempt = 0; attempt <= maxRetries; attempt++) {\n    try {\n      const startTime = Date.now();\n      \n      // Calculate delay for exponential backoff\n      if (attempt > 0) {\n        const delay = Math.min(\n          config.http.baseDelay * Math.pow(config.http.backoffMultiplier, attempt - 1),\n          config.http.maxDelay\n        );\n        await new Promise(resolve => setTimeout(resolve, delay));\n      }\n\n      // Make HTTP request (simulated - in real N8N this would be actual HTTP call)\n      const response = await fetch(url, {\n        method: 'GET',\n        timeout: config.http.timeout,\n        headers: {\n          'User-Agent': 'N8N-Health-Monitor/1.0',\n          'Accept': 'application/json'\n        }\n      });\n\n      const responseTime = Date.now() - startTime;\n      \n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      \n      return {\n        success: true,\n        data: data,\n        responseTime: responseTime,\n        attempt: attempt + 1,\n        timestamp: new Date().toISOString()\n      };\n      \n    } catch (error) {\n      lastError = error;\n      console.log(`Attempt ${attempt + 1} failed: ${error.message}`);\n      \n      // Don't retry on certain errors\n      if (error.message.includes('404') || error.message.includes('401')) {\n        break;\n      }\n    }\n  }\n  \n  return {\n    success: false,\n    error: lastError.message,\n    attempt: maxRetries + 1,\n    timestamp: new Date().toISOString()\n  };\n}\n\n// Main execution logic\nconst endpoint = config.endpoints[0];\nconst circuitBreaker = new CircuitBreaker(endpoint, circuitState[0]);\n\n// Check if circuit breaker allows execution\nif (!circuitBreaker.canExecute()) {\n  return [{\n    json: {\n      status: 'circuit_open',\n      message: 'Circuit breaker is OPEN - skipping health check',\n      endpoint: endpoint.name,\n      nextAttemptTime: circuitBreaker.nextAttemptTime,\n      timestamp: new Date().toISOString()\n    }\n  }];\n}\n\n// Simulate the HTTP request (in real N8N, you'd use the HTTP Request node)\n// For this example, we'll create a mock response\nconst mockHealthResponse = {\n  success: true,\n  data: {\n    status: 'healthy',\n    services: {\n      database: 'healthy',\n      cache: 'healthy',\n      external_api: 'healthy'\n    },\n    timestamp: new Date().toISOString(),\n    version: '1.0.0'\n  },\n  responseTime: 150,\n  attempt: 1,\n  timestamp: new Date().toISOString()\n};\n\n// Process the result\nif (mockHealthResponse.success) {\n  circuitBreaker.onSuccess();\n} else {\n  circuitBreaker.onFailure();\n}\n\nreturn [{\n  json: {\n    ...mockHealthResponse,\n    circuitBreakerState: {\n      state: circuitBreaker.state,\n      failureCount: circuitBreaker.failureCount,\n      lastFailureTime: circuitBreaker.lastFailureTime,\n      nextAttemptTime: circuitBreaker.nextAttemptTime\n    },\n    endpoint: endpoint.name\n  }\n}];"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [120, -180],
      "id": "enhanced-http-request",
      "name": "Enhanced HTTP Request with Retry"
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "-- Update circuit breaker state\nMERGE CircuitBreakerState AS target\nUSING (SELECT \n  '{{$json.endpoint}}' as endpoint_name,\n  '{{$json.circuitBreakerState.state}}' as state,\n  {{$json.circuitBreakerState.failureCount}} as failure_count,\n  '{{$json.circuitBreakerState.lastFailureTime}}' as last_failure_time,\n  '{{$json.circuitBreakerState.nextAttemptTime}}' as next_attempt_time\n) AS source ON target.endpoint_name = source.endpoint_name\nWHEN MATCHED THEN\n  UPDATE SET \n    state = source.state,\n    failure_count = source.failure_count,\n    last_failure_time = source.last_failure_time,\n    next_attempt_time = source.next_attempt_time,\n    updated_at = GETDATE()\nWHEN NOT MATCHED THEN\n  INSERT (endpoint_name, state, failure_count, last_failure_time, next_attempt_time, created_at, updated_at)\n  VALUES (source.endpoint_name, source.state, source.failure_count, source.last_failure_time, source.next_attempt_time, GETDATE(), GETDATE());"
      },
      "type": "n8n-nodes-base.microsoftSql",
      "typeVersion": 1.1,
      "position": [320, -180],
      "id": "update-circuit-breaker",
      "name": "Update Circuit Breaker State"
    },
    {
      "parameters": {
        "jsCode": "// Enhanced notification content generator\nconst item = $input.item.json;\n\nfunction getSeverityLevel(status, responseTime, circuitState) {\n  if (status === 'circuit_open' || !item.success) {\n    return 'CRITICAL';\n  }\n  if (responseTime > 5000) {\n    return 'WARNING';\n  }\n  return 'INFO';\n}\n\nfunction formatDuration(ms) {\n  if (ms < 1000) return `${ms}ms`;\n  if (ms < 60000) return `${(ms/1000).toFixed(1)}s`;\n  return `${(ms/60000).toFixed(1)}m`;\n}\n\nfunction generateAlertContent(data) {\n  const severity = getSeverityLevel(data.status, data.responseTime, data.circuitBreakerState);\n  const config = {\n    CRITICAL: { emoji: \"🔴\", color: \"#FF0000\", priority: \"HIGH\" },\n    WARNING: { emoji: \"🟡\", color: \"#FFA500\", priority: \"MEDIUM\" },\n    INFO: { emoji: \"🟢\", color: \"#00FF00\", priority: \"LOW\" }\n  };\n  \n  const severityConfig = config[severity];\n  const timestamp = new Date().toLocaleString();\n  \n  // Email content\n  const emailSubject = `${severityConfig.emoji} ${severity}: ${data.endpoint || 'Health Check'} - ${data.success ? 'Recovered' : 'Issue Detected'}`;\n  \n  const emailBody = `\n${severityConfig.emoji} SYSTEM ALERT - ${severity} PRIORITY\n\n📊 SUMMARY:\n• Status: ${data.success ? '✅ HEALTHY' : '❌ UNHEALTHY'}\n• Endpoint: ${data.endpoint || 'Unknown'}\n• Response Time: ${data.responseTime ? formatDuration(data.responseTime) : 'N/A'}\n• Timestamp: ${timestamp}\n• Attempt: ${data.attempt || 1}\n\n🔧 TECHNICAL DETAILS:\n• Circuit Breaker: ${data.circuitBreakerState?.state || 'UNKNOWN'}\n• Failure Count: ${data.circuitBreakerState?.failureCount || 0}\n• Last Failure: ${data.circuitBreakerState?.lastFailureTime || 'None'}\n\n${data.success ? '' : `❌ ERROR DETAILS:\n• Message: ${data.error || 'Unknown error'}\n• Type: ${data.status === 'circuit_open' ? 'Circuit Breaker Open' : 'HTTP Request Failed'}\n`}\n🎯 AFFECTED SERVICES:\n${data.data?.services ? Object.entries(data.data.services).map(([k,v]) => `• ${k}: ${v}`).join('\\n') : '• Status unknown'}\n\n📋 NEXT STEPS:\n${severity === 'CRITICAL' ? \n  '1. Check application logs immediately\\n2. Verify database connectivity\\n3. Review recent deployments\\n4. Contact on-call engineer if needed' :\n  '1. Monitor for continued issues\\n2. Review performance metrics\\n3. Consider scaling if needed'\n}\n\n🔗 USEFUL LINKS:\n• Dashboard: https://your-dashboard.com\n• Logs: https://your-logs.com\n• Runbook: https://wiki.com/incident-response\n\n---\nGenerated by N8N Health Monitor v2.0\n  `;\n  \n  // Telegram content (shorter format)\n  const telegramMessage = `\n${severityConfig.emoji} *${severity} ALERT*\n\n*${data.endpoint || 'Health Check'}*: ${data.success ? '✅ RECOVERED' : '❌ DOWN'}\n\n📊 *Quick Stats:*\n• Response: ${data.responseTime ? formatDuration(data.responseTime) : 'TIMEOUT'}\n• Circuit: ${data.circuitBreakerState?.state || 'UNKNOWN'}\n• Time: ${timestamp}\n\n${data.success ? '' : `⚠️ *Error:* ${data.error || 'Unknown'}\n`}\n🔗 [Dashboard](https://your-dashboard.com) | [Logs](https://your-logs.com)\n  `;\n  \n  return {\n    severity,\n    email: {\n      subject: emailSubject,\n      body: emailBody\n    },\n    telegram: {\n      message: telegramMessage,\n      parse_mode: 'Markdown'\n    },\n    metadata: {\n      priority: severityConfig.priority,\n      color: severityConfig.color,\n      timestamp: timestamp,\n      shouldAlert: severity !== 'INFO' || !data.success\n    }\n  };\n}\n\nconst alertContent = generateAlertContent(item);\n\nreturn [{ json: { ...item, alertContent } }];"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [520, -180],
      "id": "generate-alert-content",
      "name": "Generate Enhanced Alert Content"
    },
    {
      "parameters": {
        "conditions": {\n          \"options\": {\n            \"caseSensitive\": true,\n            \"leftValue\": \"\",\n            \"typeValidation\": \"strict\",\n            \"version\": 2\n          },\n          \"conditions\": [\n            {\n              \"id\": \"should-alert\",\n              \"leftValue\": \"={{ $json.alertContent.metadata.shouldAlert }}\",\n              \"rightValue\": \"true\",\n              \"operator\": {\n                \"type\": \"boolean\",\n                \"operation\": \"equals\"\n              }\n            }\n          ]\n        }\n      },\n      \"type\": \"n8n-nodes-base.if\",\n      \"typeVersion\": 2.2,\n      \"position\": [720, -180],\n      \"id\": \"should-send-alert\",\n      \"name\": \"Should Send Alert?\"\n    },\n    {\n      \"parameters\": {\n        \"sendTo\": \"<EMAIL>\",\n        \"subject\": \"={{ $json.alertContent.email.subject }}\",\n        \"emailType\": \"text\",\n        \"message\": \"={{ $json.alertContent.email.body }}\",\n        \"options\": {}\n      },\n      \"type\": \"n8n-nodes-base.gmail\",\n      \"typeVersion\": 2.1,\n      \"position\": [920, -280],\n      \"id\": \"send-enhanced-email\",\n      \"name\": \"Send Enhanced Email Alert\",\n      \"credentials\": {\n        \"gmailOAuth2\": {\n          \"id\": \"QWKw5s7BDQMVi3FB\",\n          \"name\": \"Gmail account\"\n        }\n      }\n    },\n    {\n      \"parameters\": {\n        \"chatId\": \"*********\",\n        \"text\": \"={{ $json.alertContent.telegram.message }}\",\n        \"additionalFields\": {\n          \"parse_mode\": \"Markdown\"\n        }\n      },\n      \"type\": \"n8n-nodes-base.telegram\",\n      \"typeVersion\": 1,\n      \"position\": [920, -80],\n      \"name\": \"Send Enhanced Telegram Alert\",\n      \"id\": \"send-enhanced-telegram\",\n      \"credentials\": {\n        \"telegramApi\": {\n          \"id\": \"ABLc38odkR8y7l9G\",\n          \"name\": \"Telegram account\"\n        }\n      }\n    }\n  ],\n  \"connections\": {
    "Schedule Trigger": {
      "main": [
        [
          {
            "node": "Configuration",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Configuration": {
      "main": [
        [
          {
            "node": "Check Circuit Breaker",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Check Circuit Breaker": {
      "main": [
        [
          {
            "node": "Enhanced HTTP Request with Retry",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Enhanced HTTP Request with Retry": {
      "main": [
        [
          {
            "node": "Update Circuit Breaker State",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Update Circuit Breaker State": {
      "main": [
        [
          {
            "node": "Generate Enhanced Alert Content",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Generate Enhanced Alert Content": {
      "main": [
        [
          {
            "node": "Should Send Alert?",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Should Send Alert?": {
      "main": [
        [
          {
            "node": "Send Enhanced Email Alert",
            "type": "main",
            "index": 0
          },
          {
            "node": "Send Enhanced Telegram Alert",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "active": false,
  "settings": {
    "executionOrder": "v1"
  }
}
