{"name": "healthCheck", "nodes": [{"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 3}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-480, -180], "id": "43777f46-fa19-46c8-89a4-5aa77445db2e", "name": "Schedule Trigger"}, {"parameters": {"url": "https://nodejs-serverless-function-express-tau-livid.vercel.app/api/health", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-260, -80], "id": "3dc3cc27-d5b8-4fa4-a3f2-dee3b4c2a03f", "name": "HTTP Request", "onError": "continueRegularOutput"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4o-mini"}, "messages": {"values": [{"content": "=summarize the problem in this make it clear and easy to understand and suggest solution for it{{ $json.result }}services"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [380, -320], "id": "c12cafe4-e104-4f20-bf67-1ec9137d0110", "name": "Message a model", "credentials": {"openAiApi": {"id": "xsbntiGV2JXS0NZH", "name": "n8n free OpenAI API credits"}}}, {"parameters": {"jsCode": "const items = $input.all();\nconst formattedItems = items.map((item) => {\n  const status = `Status: ${item?.json?.status}`;\n  const summary = `Summary: ${item?.json?.summary}`;\n  const issuesList = Array.isArray(item?.json?.issues) ? item.json.issues : [];\n  const issues = `Issues: ${issuesList.join(\", \")}`;\n  const services = `Services: ${Object.entries(item?.json?.services)\n    .map(([key, value]) => `${key}: ${value}`)\n    .join(\", \")}`;\n  const timestamp = `Timestamp: ${item?.json?.timestamp}`;\n  return {\n    result: `${status}\\n${summary}\\n${issues}\\n${services}\\n${timestamp}`,\n  };\n});\nreturn formattedItems;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [180, -320], "id": "3a946d26-91ed-4f94-a578-9f4089857600", "name": "Code"}, {"parameters": {"sendTo": "<EMAIL>", "subject": "Critical , system have a problem", "emailType": "text", "message": "🚨 SYSTEM ALERT!\n\nSummary:\n{{$json[\"message\"][\"content\"]}}\n\n-----------------------\n🧱 Issues:\n{{ $node[\"HTTP Request\"].json[\"issues\"].join(\", \") }}\n\n🔧 Services:\n{{ Object.entries($node[\"HTTP Request\"].json[\"services\"]).map(([k,v]) => k + ': ' + v).join(', ') }}", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [760, -320], "id": "ad216ca3-bfa1-44a6-a349-ed69df7a9ac8", "name": "Send a message", "webhookId": "73bd8c66-4227-469c-a14a-271b6e77c1af", "credentials": {"gmailOAuth2": {"id": "QWKw5s7BDQMVi3FB", "name": "Gmail account"}}}, {"parameters": {"chatId": "*********", "text": "🚨 SYSTEM ALERT:\n{{$json[\"message\"][\"content\"]}}\n\n🧱 Issues:\n{{$node[\"HTTP Request\"].json[\"issues\"]}}\n\n🔧 Services:\n{{ Object.entries($node[\"HTTP Request\"].json[\"services\"]).map(([k,v]) => k + ': ' + v).join(', ') }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [980, -320], "name": "Send Telegram Alert", "id": "e8837828-d19d-4497-a321-ef61953f9bff", "webhookId": "ba88fa57-a785-41b8-b7ab-88a095950b92", "credentials": {"telegramApi": {"id": "ABLc38odkR8y7l9G", "name": "Telegram account"}}}, {"parameters": {"operation": "execute<PERSON>uery"}, "type": "n8n-nodes-base.microsoftSql", "typeVersion": 1.1, "position": [-260, -280], "id": "dc2e377b-73ca-48d2-8f79-a561cdd742ff", "name": "Microsoft SQL"}, {"parameters": {"rule": {"interval": [{"daysInterval": 7}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-420, 240], "id": "c77237c6-621e-4eec-906b-634708a45c9c", "name": "Schedule Trigger1"}, {"parameters": {"sendTo": "<EMAIL>", "subject": "Critical , system have a problem", "emailType": "text", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [560, 220], "id": "48170e8b-c9ca-4813-91a8-162264f8d01d", "name": "Send a message1", "webhookId": "73bd8c66-4227-469c-a14a-271b6e77c1af", "credentials": {"gmailOAuth2": {"id": "QWKw5s7BDQMVi3FB", "name": "Gmail account"}}}, {"parameters": {"chatId": "*********", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [780, 220], "name": "Send Telegram Alert1", "id": "52bbbc46-19f5-48e5-bde5-68ac33b385f4", "webhookId": "ba88fa57-a785-41b8-b7ab-88a095950b92", "credentials": {"telegramApi": {"id": "ABLc38odkR8y7l9G", "name": "Telegram account"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4o-mini"}, "messages": {"values": [{"content": "=summarize the problem in this make it clear and easy to understand and suggest solution for it{{ $json.result }}services"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [80, 240], "id": "33b5c8c7-e208-4bcf-ad3a-5e6004926266", "name": "Message a model1", "credentials": {"openAiApi": {"id": "xsbntiGV2JXS0NZH", "name": "n8n free OpenAI API credits"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE SystemStatus SET status = 'unhealthy' WHERE id = 1;"}, "type": "n8n-nodes-base.microsoftSql", "typeVersion": 1.1, "position": [1240, -480], "id": "8f12c757-9a3c-4ea3-9cb1-91bee24b55da", "name": "CurrentSystemStatus_Update"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO SystemStatusHistory (status, issues, services, summary)\nVALUES (\n  '{{$json[\"status\"]}}',\n  '{{$json[\"issues\"].join(\", \")}}',\n  '{{ Object.entries($json[\"services\"]).map(([k,v]) => `${k}: ${v}`).join(\", \") }}',\n  '{{$node[\"Message a model\"].json[\"message\"][\"content\"]}}'\n);\n"}, "type": "n8n-nodes-base.microsoftSql", "typeVersion": 1.1, "position": [1260, -180], "id": "16721d45-a7e8-4b3b-a53a-c0510d499f6e", "name": "SystemStatusHistory Update"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM SystemStatusHistory\nWHERE timestamp >= DATEADD(day, -7, GETDATE())\nORDER BY timestamp DESC;"}, "type": "n8n-nodes-base.microsoftSql", "typeVersion": 1.1, "position": [-200, 240], "id": "744915ea-2eaa-4f6b-bd16-d20b548caf23", "name": "GetLastWeekHistory"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "e7393f57-9196-4d35-996f-735d750cca9b", "leftValue": "={{ $json.status }}", "rightValue": "", "operator": {"type": "string", "operation": "notEquals"}}, {"id": "55f53338-6385-4550-8bb8-acd4278357c1", "leftValue": "", "rightValue": "", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "or"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-20, -180], "id": "783ff71a-cab8-4b18-87cf-97a26aa74b1e", "name": "ifCurrentStateNotLIkePreviousState"}, {"parameters": {"jsCode": "const item = $input.item;\nconst output = {\n  status: \"unknown\",\n  summary: \"No summary available\",\n  issues: [],\n  services: {},\n  timestamp: new Date().toISOString(),\n};\n\ntry {\n  if (item.json.status) {\n    return [{ json: item.json }];\n  }\n\n  const errorMsg = item.json?.error?.message;\n  if (typeof errorMsg === \"string\" && errorMsg.includes(\"{\")) {\n    // Extract JSON from messages like: '500 - \"{JSON_CONTENT}\"'\n    const match = errorMsg.match(/\"({[\\s\\S]*})\"/); \n    if (match && match[1]) {\n      // Unescape the JSON string\n      const jsonString = match[1].replace(/\\\\\"/g, '\"');\n      const parsed = JSON.parse(jsonString);\n      output.status = parsed.status || \"unhealthy\";\n      output.summary = parsed.summary || \"System issue detected\";\n      output.issues = parsed.issues || [];\n      output.services = parsed.services || {};\n      output.timestamp = parsed.timestamp || output.timestamp;\n      return [{ json: output }];\n    }\n    \n    // Fallback: try direct JSON match\n    const directMatch = errorMsg.match(/{[\\s\\S]*}/);\n    if (directMatch && directMatch[0]) {\n      const parsed = JSON.parse(directMatch[0]);\n      output.status = parsed.status || \"unhealthy\";\n      output.summary = parsed.summary || \"System issue detected\";\n      output.issues = parsed.issues || [];\n      output.services = parsed.services || {};\n      output.timestamp = parsed.timestamp || output.timestamp;\n      return [{ json: output }];\n    }\n  }\n\n  output.status = \"unhealthy\";\n  output.summary = errorMsg?.slice(0, 300) || \"Unrecognized server error.\";\n} catch (err) {\n  output.status = \"unhealthy\";\n  output.summary = `Parse error: ${err.message}`;\n}\n\nreturn [{ json: output }];"}, "name": "Handle API Error", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-40, -80], "id": "870a297e-7517-401d-ad76-cacdcfc495f0"}], "pinData": {}, "connections": {"Schedule Trigger": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}, {"node": "Microsoft SQL", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Handle API Error", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Message a model", "type": "main", "index": 0}]]}, "Message a model": {"main": [[{"node": "Send a message", "type": "main", "index": 0}]]}, "Send a message": {"main": [[{"node": "Send Telegram Alert", "type": "main", "index": 0}]]}, "Send Telegram Alert": {"main": [[{"node": "SystemStatusHistory Update", "type": "main", "index": 0}, {"node": "CurrentSystemStatus_Update", "type": "main", "index": 0}]]}, "Microsoft SQL": {"main": [[{"node": "ifCurrentStateNotLIkePreviousState", "type": "main", "index": 0}]]}, "Schedule Trigger1": {"main": [[{"node": "GetLastWeekHistory", "type": "main", "index": 0}]]}, "Send a message1": {"main": [[{"node": "Send Telegram Alert1", "type": "main", "index": 0}]]}, "Message a model1": {"main": [[{"node": "Send a message1", "type": "main", "index": 0}]]}, "SystemStatusHistory Update": {"main": [[]]}, "GetLastWeekHistory": {"main": [[{"node": "Message a model1", "type": "main", "index": 0}]]}, "ifCurrentStateNotLIkePreviousState": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Handle API Error": {"main": [[{"node": "ifCurrentStateNotLIkePreviousState", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "be783ede-cae3-40d5-8845-31deb3719190", "meta": {"instanceId": "5a41d6b80f4619ebc9780d9a352cd73032290178972ecace3ee8f4b19099b358"}, "id": "FOJs8WltxSZxuPBI", "tags": []}